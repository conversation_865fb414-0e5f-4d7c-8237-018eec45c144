import { NextResponse } from 'next/server';
import { getDynamicModel, validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';
import { esClient } from '@/lib/elasticsearch';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    const database = rawDatabase.toLowerCase();

    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    const config = await getDatabaseConfig(database);
    if (!config) {
      return NextResponse.json(
        { success: false, error: '未找到数据库配置' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);

    // 使用全局翻页配置（性能优化）
    const requestedPage = parseInt(searchParams.get('page') || '1', 10);
    const requestedLimit = parseInt(searchParams.get('limit') || '0', 10);

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';
    const filtersParam = searchParams.get('filters');
    const allFieldsQuery = searchParams.get('allFields')?.trim();

    // 如果有 allFields 查询，尝试使用 ES 搜索
    if (allFieldsQuery) {
      try {
        console.log('Using ES search for allFields:', allFieldsQuery, 'database:', database);

        const esResponse = await fetch(
          `${request.url.split('/api/')[0]}/api/medical-search?q=${encodeURIComponent(allFieldsQuery)}&database=${database}&page=${page}&limit=${limit}`
        );

        if (esResponse.ok) {
          const esData = await esResponse.json();

          if (esData.success && esData.data) {
            console.log('ES search successful, transforming results');

            // 转换 ES 结果为标准格式
            const transformedData = esData.data.hits.map((hit: any) => ({
              id: hit._source.id,
              ...hit._source
            }));

            return NextResponse.json({
              success: true,
              data: transformedData,
              config: config,
              pagination: {
                page: esData.data.page,
                limit: esData.data.limit,
                totalCount: esData.data.total,
                totalPages: esData.data.totalPages,
                hasNext: esData.data.page < esData.data.totalPages,
                hasPrev: esData.data.page > 1,
                maxPages: 100,
                isAtMaxPages: esData.data.totalPages >= 100,
                maxPageSize: 100,
                defaultPageSize: 20,
              }
            });
          } else {
            console.warn('ES search returned no data, falling back to Prisma');
          }
        } else {
          console.warn('ES search response not ok, falling back to Prisma');
        }
      } catch (esError) {
        console.warn('ES search failed, falling back to Prisma:', esError);
      }
    }

    const model = await getDynamicModel(database);

    // 验证 sortBy 字段是否有效
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 构建排序条件
    let orderBy: Record<string, 'asc' | 'desc'> = {};

    if (sortBy && sortableFields.includes(sortBy)) {
      // 如果指定了有效的排序字段，使用指定的排序
      orderBy = { [sortBy]: sortOrder };
    } else if (config.defaultSort && config.defaultSort.length > 0) {
      // 使用配置中的默认排序
      if (config.defaultSort.length === 1) {
        const sort = config.defaultSort[0];
        orderBy = { [sort.field]: sort.order };
      } else {
        // 多字段排序
        orderBy = config.defaultSort.map(sort => ({
          [sort.field]: sort.order
        })) as any;
      }
    } else {
      // 回退到第一个可排序字段或id
      if (sortableFields.includes('id')) {
        orderBy = { id: sortOrder };
      } else if (sortableFields.length > 0) {
        orderBy = { [sortableFields[0]]: sortOrder };
      }
    }

    let where = {};
    
    if (filtersParam) {
      try {
        const parsedFilters = JSON.parse(filtersParam);

        // 构建Prisma查询条件
        const whereConditions: Record<string, unknown> = {};
        
        Object.entries(parsedFilters).forEach(([fieldName, value]) => {
          // 查找字段配置
          const fieldConfig = config.fields.find(f => f.fieldName === fieldName);
          
          if (fieldConfig) {
            // 处理多选字段
            if (Array.isArray(value) && value.length > 0) {
              // 检查是否包含N/A值
              const processedValues = value.map(v => v === 'N/A' ? null : v);
              if (processedValues.includes(null)) {
                // 如果包含N/A，需要特殊处理
                const nonNullValues = processedValues.filter(v => v !== null);
                if (nonNullValues.length > 0) {
                  // 既有具体值又有N/A，使用OR条件
                  whereConditions.OR = [
                    { [fieldName]: { in: nonNullValues } },
                    { [fieldName]: null },
                    { [fieldName]: '' }
                  ];
                } else {
                  // 只选择了N/A
                  whereConditions.OR = [
                    { [fieldName]: null },
                    { [fieldName]: '' }
                  ];
                }
              } else {
                whereConditions[fieldName] = {
                  in: value
                };
              }
            }
            // 处理单选字段
            else if (value && typeof value === 'string' && value.trim() !== '') {
              if (value === 'N/A') {
                // 单选字段选择了N/A
                whereConditions.OR = [
                  { [fieldName]: null },
                  { [fieldName]: '' }
                ];
              } else if (fieldConfig.searchType === 'exact') {
                whereConditions[fieldName] = value;
              } else if (fieldConfig.searchType === 'contains') {
                whereConditions[fieldName] = {
                  contains: value,
                  mode: 'insensitive' // 不区分大小写
                };
              } else {
                // 默认使用contains搜索
                whereConditions[fieldName] = {
                  contains: value,
                  mode: 'insensitive'
                };
              }
            }
          } else {
            // 如果没有找到字段配置，使用默认的contains搜索
            if (Array.isArray(value) && value.length > 0) {
              whereConditions[fieldName] = {
                in: value
              };
            } else if (value && typeof value === 'string' && value.trim() !== '') {
              whereConditions[fieldName] = {
                contains: value,
                mode: 'insensitive'
              };
            }
          }
        });
        
        // 合并筛选条件和数据库过滤条件
        where = { ...where, ...whereConditions };
      } catch (_e) {
        console.error("解析筛选条件失败:", _e);
        // 保持现有的 where 条件
      }
    }

    const select: Record<string, unknown> = {};
    config.fields
      .filter(f => f.isVisible)
      .forEach(f => {
        select[f.fieldName] = true;
      });
    select['id'] = true;

    const data = await (model as any).findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    });

    const total = await (model as any).count({ where });

    return NextResponse.json({
      success: true,
      data,
      config,
      pagination: buildPaginationResponse(page, limit, total)
    });
  } catch (__error) {
    console.error(`数据 API 错误:`, __error);
    return NextResponse.json(
      { success: false, error: '内部服务器错误' },
      { status: 500 }
    );
  }
} 