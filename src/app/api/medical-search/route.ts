import { type NextRequest, NextResponse } from 'next/server';
import { esClient } from '@/lib/elasticsearch';

export const dynamic = 'force-dynamic';

// 医疗设备搜索结果接口
interface MedicalSearchHit {
  _id: string;
  _source: {
    id: string;
    table_code: string;
    registration_no?: string;
    product_combined?: string;
    company_combined?: string;
  };
}

interface MedicalSearchResponse {
  success: boolean;
  data?: {
    hits: MedicalSearchHit[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  error?: string;
}

/**
 * GET /api/medical-search?q=<keyword>&database=<code>&page=1&limit=20
 * 搜索医疗设备索引，返回具体的搜索结果
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const searchParams = req.nextUrl.searchParams;
    const query = searchParams.get('q')?.trim();
    const database = searchParams.get('database')?.trim(); // 数据库代码过滤
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);

    if (!query) {
      return NextResponse.json({
        success: false,
        error: 'Search query is required'
      }, { status: 400 });
    }

    const from = (page - 1) * limit;

    // 构建查询条件 - 使用与全局搜索相同的精确匹配
    const mustQueries = [];

    // 数据库过滤（必须匹配）
    if (database) {
      mustQueries.push({
        term: { 'table_code': database }
      });
    }

    // 搜索查询（至少匹配一个）
    const shouldQueries = [
      // 精确短语匹配（最高权重）
      {
        multi_match: {
          query: query,
          fields: [
            'registration_no.raw^5',
            'product_combined^3',
            'company_combined^3'
          ],
          type: 'phrase',
          boost: 5
        }
      },
      // 包含匹配（中等权重）
      {
        multi_match: {
          query: query,
          fields: [
            'registration_no.*^2',
            'product_combined.*^2',
            'company_combined.*^2'
          ],
          type: 'cross_fields',
          operator: 'and',
          boost: 2
        }
      },
      // 通配符匹配（用于部分匹配）
      {
        bool: {
          should: [
            { wildcard: { 'registration_no.raw': `*${query.toLowerCase()}*` } },
            { wildcard: { 'product_combined': `*${query.toLowerCase()}*` } },
            { wildcard: { 'company_combined': `*${query.toLowerCase()}*` } }
          ],
          boost: 1
        }
      }
    ];

    mustQueries.push({
      bool: {
        should: shouldQueries,
        minimum_should_match: 1
      }
    });

    // 构建 Elasticsearch 查询
    const searchBody = {
      query: {
        bool: {
          must: mustQueries
        }
      },
      from: from,
      size: limit,
      track_total_hits: true,
      _source: [
        'id',
        'table_code',
        'registration_no',
        'product_combined',
        'company_combined'
      ]
    };

    console.log('Searching medical_index with query:', JSON.stringify(searchBody, null, 2));

    const response = await esClient.search({
      index: 'medical_index',
      body: searchBody
    });

    // 处理 ES 8.x 响应格式
    const responseBody = (response as any).body || response;
    const hits = responseBody.hits?.hits || [];
    const total = responseBody.hits?.total?.value || 0;
    const totalPages = Math.ceil(total / limit);

    const result: MedicalSearchResponse = {
      success: true,
      data: {
        hits: hits as MedicalSearchHit[],
        total,
        page,
        limit,
        totalPages
      }
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('Medical search error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Search failed' 
    }, { status: 500 });
  }
}

/**
 * POST /api/medical-search
 * 支持更复杂的搜索条件
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { 
      query, 
      filters = {}, 
      page = 1, 
      limit = 20,
      sortBy = '_score',
      sortOrder = 'desc'
    } = body;

    if (!query) {
      return NextResponse.json({ 
        success: false, 
        error: 'Search query is required' 
      }, { status: 400 });
    }

    const from = (page - 1) * Math.min(limit, 100);

    // 构建查询条件
    const mustQueries = [];
    
    // 主搜索查询
    mustQueries.push({
      multi_match: {
        query: query,
        fields: [
          'registration_no.*',
          'product_combined.*', 
          'company_combined.*'
        ],
        type: 'best_fields',
        fuzziness: 'AUTO'
      }
    });

    // 添加过滤条件
    if (filters.table_code) {
      mustQueries.push({
        term: { 'table_code': filters.table_code }
      });
    }

    if (filters.registration_no) {
      mustQueries.push({
        wildcard: { 
          'registration_no.raw': `*${filters.registration_no}*` 
        }
      });
    }

    // 构建排序
    const sort = [];
    if (sortBy === '_score') {
      sort.push({ '_score': { order: sortOrder } });
    } else {
      sort.push({ [sortBy]: { order: sortOrder } });
    }

    const searchBody = {
      query: {
        bool: {
          must: mustQueries
        }
      },
      from: from,
      size: Math.min(limit, 100),
      sort: sort,
      track_total_hits: true,
      _source: [
        'id',
        'table_code', 
        'registration_no',
        'product_combined',
        'company_combined'
      ]
    };

    console.log('Advanced medical search:', JSON.stringify(searchBody, null, 2));

    const response = await esClient.search({
      index: 'medical_index',
      body: searchBody
    });

    // 处理 ES 8.x 响应格式
    const responseBody = (response as any).body || response;
    const hits = responseBody.hits?.hits || [];
    const total = responseBody.hits?.total?.value || 0;
    const totalPages = Math.ceil(total / Math.min(limit, 100));

    const result: MedicalSearchResponse = {
      success: true,
      data: {
        hits: hits as MedicalSearchHit[],
        total,
        page,
        limit: Math.min(limit, 100),
        totalPages
      }
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('Advanced medical search error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Advanced search failed' 
    }, { status: 500 });
  }
}
