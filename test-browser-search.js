// 浏览器测试脚本 - 在浏览器控制台中运行
// 访问 http://localhost:3001/data/list/us_class 然后在控制台运行此脚本

async function testBrowserSearch() {
  console.log('🔍 测试浏览器中的搜索功能...\n');

  try {
    // 1. 测试devicename字段搜索
    console.log('📋 测试devicename字段搜索:');
    
    // 查找搜索输入框
    const searchInputs = document.querySelectorAll('input[placeholder*="devicename"], input[placeholder*="Device"]');
    if (searchInputs.length > 0) {
      const searchInput = searchInputs[0];
      console.log('✅ 找到搜索输入框');
      
      // 输入搜索词
      searchInput.value = 'Dental';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      // 查找搜索按钮
      const searchButtons = document.querySelectorAll('button');
      const searchButton = Array.from(searchButtons).find(btn => 
        btn.textContent.includes('Apply') || 
        btn.textContent.includes('Search') ||
        btn.textContent.includes('搜索')
      );
      
      if (searchButton) {
        console.log('✅ 找到搜索按钮，执行搜索...');
        searchButton.click();
        
        // 等待搜索结果
        setTimeout(() => {
          const resultRows = document.querySelectorAll('tbody tr');
          console.log(`📊 搜索结果: ${resultRows.length} 行`);
          
          if (resultRows.length > 0) {
            console.log('🔍 前3行结果:');
            Array.from(resultRows).slice(0, 3).forEach((row, index) => {
              const cells = row.querySelectorAll('td');
              if (cells.length > 0) {
                console.log(`${index + 1}. ${cells[0].textContent.trim()}`);
              }
            });
          }
        }, 2000);
      } else {
        console.log('❌ 未找到搜索按钮');
      }
    } else {
      console.log('❌ 未找到搜索输入框');
    }

    // 2. 测试全文搜索
    console.log('\n📋 测试全文搜索:');
    setTimeout(() => {
      // 查找全文搜索输入框
      const allFieldsInputs = document.querySelectorAll('input[placeholder*="search"], input[placeholder*="Search"]');
      if (allFieldsInputs.length > 0) {
        const allFieldsInput = allFieldsInputs[0];
        console.log('✅ 找到全文搜索输入框');
        
        allFieldsInput.value = 'Dental';
        allFieldsInput.dispatchEvent(new Event('input', { bubbles: true }));
        allFieldsInput.dispatchEvent(new KeyboardEvent('keypress', { key: 'Enter', bubbles: true }));
        
        setTimeout(() => {
          const resultRows = document.querySelectorAll('tbody tr');
          console.log(`📊 全文搜索结果: ${resultRows.length} 行`);
        }, 2000);
      } else {
        console.log('❌ 未找到全文搜索输入框');
      }
    }, 3000);

  } catch (error) {
    console.error('❌ 浏览器测试错误:', error);
  }
}

// 运行测试
testBrowserSearch();

// 提供手动测试指令
console.log(`
🔧 手动测试指令:
1. 访问: http://localhost:3001/data/list/us_class
2. 在左侧筛选面板中找到 "devicename" 输入框
3. 输入 "Dental" 并点击 "Apply Filters" 按钮
4. 检查搜索结果是否正确显示
5. 尝试使用页面顶部的全文搜索框
6. 验证搜索结果中的 devicename 字段是否正确显示

✅ 预期结果:
- 应该能找到包含 "Dental" 的设备记录
- devicename 字段应该正确显示，不应该是 undefined
- 搜索结果应该与API测试结果一致
`);
